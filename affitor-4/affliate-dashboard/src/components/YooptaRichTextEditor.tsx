import React, { useMemo, useRef, useCallback, Component, ErrorInfo, ReactNode } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createYooptaEditor } from '@yoopta/editor';
import YooptaEditor from '@yoopta/editor';
import { actions, selectImageUploadLoading, selectImageUploadError } from '@/features/page/page.slice';

// Core plugins for rich text editing
import Paragraph from '@yoopta/paragraph';
import Blockquote from '@yoopta/blockquote';
import Headings from '@yoopta/headings';
import Lists from '@yoopta/lists';
import Link from '@yoopta/link';
import Image from '@yoopta/image';
import Callout from '@yoopta/callout';
import Code from '@yoopta/code';
import Divider from '@yoopta/divider';
import Table from '@yoopta/table';

// Tools for Notion-like UX
import ActionMenu, { DefaultActionMenuRender } from '@yoopta/action-menu-list';
import Toolbar, { DefaultToolbarRender } from '@yoopta/toolbar';
import LinkTool, { DefaultLinkToolRender } from '@yoopta/link-tool';

// Marks for inline formatting
import { Bold, Italic, CodeMark, Underline, Strike, Highlight } from '@yoopta/marks';

interface YooptaRichTextEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  onSave?: (content: any) => void;
  autoSave?: boolean;
  autoSaveInterval?: number;
  loading?: boolean;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
  pageId?: string; // Required for image uploads
}

// Define base plugins outside component to prevent recreation
const BASE_PLUGINS = [
  Paragraph,
  Headings.HeadingOne,
  Headings.HeadingTwo,
  Headings.HeadingThree,
  Blockquote,
  Callout,
  Lists.BulletedList,
  Lists.NumberedList,
  Lists.TodoList,
  Code,
  Link,
  Table,
  Divider,
];

// Define tools outside component
const TOOLS = {
  ActionMenu: {
    render: DefaultActionMenuRender,
    tool: ActionMenu,
  },
  Toolbar: {
    render: DefaultToolbarRender,
    tool: Toolbar,
  },
  LinkTool: {
    render: DefaultLinkToolRender,
    tool: LinkTool,
  },
};

// Define marks outside component
const MARKS = [Bold, Italic, CodeMark, Underline, Strike, Highlight];

// Simple Error Boundary for YooptaEditor
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[YooptaEditor] Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

const YooptaRichTextEditor: React.FC<YooptaRichTextEditorProps> = ({
  content,
  onChange,
  onSave,
  autoSave = true,
  autoSaveInterval = 30000,
  loading = false,
  placeholder = "Type '/' for commands...",
  className = '',
  readOnly = false,
  pageId,
}) => {
  // Redux hooks
  const dispatch = useDispatch();
  const imageUploadLoading = useSelector(selectImageUploadLoading);
  const imageUploadError = useSelector(selectImageUploadError);

  // Create a custom upload handler that integrates with Redux
  const handleImageUpload = React.useCallback(async (file: File): Promise<string> => {
    if (!pageId) {
      throw new Error('Page ID is required for image upload');
    }

    // Import validation utility
    const { validateImageFile } = await import('@/utils/image-upload');

    console.log('[YooptaEditor] Starting image upload:', {
      fileName: file.name,
      fileSize: file.size,
      pageId,
    });

    // Validate file on client side
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Dispatch Redux action for image upload
    dispatch(actions.uploadImageRequest({ file, pageId }));

    // Return a promise that resolves when the upload completes
    return new Promise((resolve, reject) => {
      // Create a unique identifier for this upload
      const uploadId = `${pageId}-${Date.now()}-${Math.random()}`;

      // Store the promise resolvers
      const uploadPromise = { resolve, reject, uploadId, fileName: file.name };

      // Store in a global registry (temporary solution)
      if (!(window as any).__imageUploadPromises) {
        (window as any).__imageUploadPromises = new Map();
      }
      (window as any).__imageUploadPromises.set(uploadId, uploadPromise);

      // Set a timeout to reject if upload takes too long
      setTimeout(() => {
        if ((window as any).__imageUploadPromises?.has(uploadId)) {
          (window as any).__imageUploadPromises.delete(uploadId);
          reject(new Error('Upload timeout'));
        }
      }, 30000); // 30 second timeout
    });
  }, [pageId, dispatch]);
  // Create plugins with image upload configuration
  const plugins = useMemo(() => {
    try {
      console.log('[YooptaEditor] Initializing plugins...');

      // Configure Image plugin with upload functionality
      const ImagePlugin = Image.extend({
        options: {
          async onUpload(file: File) {
            try {
              // Use our custom upload handler
              const s3Url = await handleImageUpload(file);

              console.log('[YooptaEditor] Upload successful, S3 URL:', s3Url);

              return {
                src: s3Url,
                alt: file.name,
                sizes: {
                  width: 0, // Will be determined by the browser
                  height: 0,
                },
              };
            } catch (error) {
              console.error('[YooptaEditor] Upload failed:', error);
              throw error;
            }
          },
        },
      });

      const allPlugins = [
        ...BASE_PLUGINS,
        ImagePlugin,
      ];

      console.log('[YooptaEditor] Plugins initialized successfully:', allPlugins.length);
      return allPlugins;
    } catch (error) {
      console.error('[YooptaEditor] Error initializing plugins:', error);
      return BASE_PLUGINS; // Fallback to base plugins without image upload
    }
  }, [handleImageUpload]);

  // Initialize editor with plugins - ensure plugins are ready before creating editor
  const editor = useMemo(() => {
    try {
      // Only create editor if plugins are available and not empty
      if (!plugins || plugins.length === 0) {
        console.log('[YooptaEditor] Plugins not ready, deferring editor creation');
        return null;
      }

      const editorInstance = createYooptaEditor();
      console.log('[YooptaEditor] Creating editor instance with plugins:', plugins.length);
      return editorInstance;
    } catch (error) {
      console.error('[YooptaEditor] Error creating editor instance:', error);
      // Return null instead of trying to create a fallback to prevent cascading errors
      return null;
    }
  }, [plugins]);

  // Refs for DOM manipulation and timers
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle link clicks and hover events within the editor to provide proper navigation
  React.useEffect(() => {
    const handleLinkClick = (event: Event) => {
      const target = event.target;

      // Check if target is an Element (has closest method)
      if (!target || !(target instanceof Element)) {
        return;
      }

      // Check if the clicked element is a link within the editor
      const link = target.closest('a[href]') as HTMLAnchorElement;
      const editorContainer = containerRef.current;

      if (link && editorContainer && editorContainer.contains(link)) {
        // Only handle links that are within our editor
        const href = link.getAttribute('href');

        if (href && !readOnly) {
          // Prevent the default popup behavior
          event.preventDefault();
          event.stopPropagation();

          console.log('[YooptaEditor] Link clicked:', href);

          try {
            // Validate and format URL
            let validUrl = href;
            if (!href.startsWith('http://') && !href.startsWith('https://')) {
              validUrl = href.startsWith('//') ? `https:${href}` : `https://${href}`;
            }

            // Open the link in a new tab/window
            window.open(validUrl, '_blank', 'noopener,noreferrer');
            console.log('[YooptaEditor] Link opened in new tab:', validUrl);
          } catch (error) {
            console.error('[YooptaEditor] Error opening link:', error);
            // Fallback: try to navigate in current tab
            try {
              window.location.href = href;
            } catch (fallbackError) {
              console.error('[YooptaEditor] Fallback navigation failed:', fallbackError);
            }
          }
        }
      }
    };

    const handleLinkHover = (event: Event) => {
      const target = event.target;

      // Check if target is an Element (has closest method)
      if (!target || !(target instanceof Element)) {
        return;
      }

      // Check if the hovered element is a link within the editor
      const link = target.closest('a[href]') as HTMLAnchorElement;
      const editorContainer = containerRef.current;

      if (link && editorContainer && editorContainer.contains(link)) {
        // Prevent any hover-based popups or tooltips
        event.preventDefault();
        event.stopPropagation();

        // Remove any title attribute that might show browser tooltips
        if (link.hasAttribute('title')) {
          link.removeAttribute('title');
        }

        console.log('[YooptaEditor] Link hover prevented for:', link.getAttribute('href'));
      }
    };

    // Add event listeners to the document to catch all clicks and hovers
    document.addEventListener('click', handleLinkClick, true);
    document.addEventListener('mouseenter', handleLinkHover, true);
    document.addEventListener('mouseover', handleLinkHover, true);

    return () => {
      // Cleanup event listeners
      document.removeEventListener('click', handleLinkClick, true);
      document.removeEventListener('mouseenter', handleLinkHover, true);
      document.removeEventListener('mouseover', handleLinkHover, true);
    };
  }, [readOnly]); // Re-run if readOnly changes

  // Helper function to capitalize block types for YooptaEditor
  const capitalizeBlockType = (type: string): string => {
    if (!type) return 'Paragraph';

    const typeMap: Record<string, string> = {
      'paragraph': 'Paragraph',
      'heading': 'HeadingOne',
      'heading1': 'HeadingOne',
      'heading2': 'HeadingTwo',
      'heading3': 'HeadingThree',
      'bulletedlist': 'BulletedList',
      'numberedlist': 'NumberedList',
      'quote': 'Quote',
      'code': 'Code',
      'divider': 'Divider',
      'image': 'Image',
      'video': 'Video',
      'embed': 'Embed',
      'callout': 'Callout',
      'accordion': 'Accordion',
      'table': 'Table',
      'file': 'File',
      'link': 'Link'
    };

    return typeMap[type.toLowerCase()] || type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Transform content to ensure it matches Yoopta editor format
  const transformContentForYoopta = (rawContent: any): any => {
    console.log('[YooptaEditor] transformContentForYoopta called with:', {
      rawContent,
      type: typeof rawContent,
      isObject: rawContent && typeof rawContent === 'object',
      keys: rawContent && typeof rawContent === 'object' ? Object.keys(rawContent) : null
    });

    if (!rawContent || typeof rawContent !== 'object') {
      console.log('[YooptaEditor] Invalid content, returning default');
      return createDefaultContent();
    }

    const transformedContent: any = {};

    Object.entries(rawContent).forEach(([blockId, block]: [string, any]) => {
      console.log(`[YooptaEditor] Processing block ${blockId}:`, block);

      if (!block || typeof block !== 'object') {
        console.log(`[YooptaEditor] Skipping invalid block ${blockId}`);
        return;
      }

      // Ensure the block has the required structure
      const transformedBlock: any = {
        id: block.id || blockId,
        type: capitalizeBlockType(block.type) || 'Paragraph',
        value: [],
        meta: block.meta || { order: 0, depth: 0 }
      };

      console.log(`[YooptaEditor] Block ${blockId} value:`, block.value);

      // Transform the value array
      if (Array.isArray(block.value)) {
        transformedBlock.value = block.value.map((element: any, index: number) => {
          console.log(`[YooptaEditor] Processing element ${blockId}.${index}:`, element);

          if (!element || typeof element !== 'object') {
            // Create a default text element
            console.log(`[YooptaEditor] Creating default element for ${blockId}.${index}`);
            return {
              id: `element-${index}`,
              type: 'paragraph',
              children: [{ text: '' }],
              props: { nodeType: 'block' }
            };
          }

          // SPECIAL CASE: Handle the common backend format where element is just { text: "content" }
          if (element && typeof element === 'object' && 'text' in element && !element.children && !element.id && !element.type) {
            console.log(`[YooptaEditor] Element ${blockId}.${index} is simple text object from backend, converting to proper format`);
            return {
              id: `element-${blockId}-${index}`,
              type: 'paragraph',
              children: [{ text: element.text || '' }],
              props: { nodeType: 'block' }
            };
          }

          // If element already has children, validate and use it
          if (element.children && Array.isArray(element.children)) {
            console.log(`[YooptaEditor] Element ${blockId}.${index} has children, validating structure`);

            // Ensure children have proper text structure
            const validChildren = element.children.map((child: any) => {
              if (child && typeof child === 'object' && 'text' in child) {
                return child;
              }
              return { text: '' };
            });

            return {
              id: element.id || `element-${index}`,
              type: element.type || 'paragraph',
              children: validChildren,
              props: element.props || { nodeType: 'block' }
            };
          }

          // If element has text property directly, wrap it in children
          if (element.text !== undefined) {
            console.log(`[YooptaEditor] Element ${blockId}.${index} has text property, wrapping in children`);
            return {
              id: element.id || `element-${index}`,
              type: element.type || 'paragraph',
              children: [{ text: element.text }],
              props: element.props || { nodeType: 'block' }
            };
          }

          // For other element types (images, etc.), preserve structure but ensure children
          console.log(`[YooptaEditor] Element ${blockId}.${index} is other type, preserving structure`);
          const transformedElement = {
            id: element.id || `element-${index}`,
            type: element.type || 'paragraph',
            ...element,
            props: element.props || { nodeType: 'block' }
          };

          // Ensure children array exists for text-based elements
          if (!transformedElement.children && (transformedElement.type === 'paragraph' || !transformedElement.type)) {
            transformedElement.children = [{ text: '' }];
          }

          return transformedElement;
        });
      } else {
        // If value is not an array, create a default paragraph
        console.log(`[YooptaEditor] Block ${blockId} value is not array, creating default`);
        transformedBlock.value = [{
          id: 'default-element',
          type: 'paragraph',
          children: [{ text: '' }],
          props: { nodeType: 'block' }
        }];
      }

      console.log(`[YooptaEditor] Transformed block ${blockId}:`, transformedBlock);
      transformedContent[blockId] = transformedBlock;
    });

    // If no valid blocks were created, return default content
    if (Object.keys(transformedContent).length === 0) {
      console.log('[YooptaEditor] No valid blocks created, returning default content');
      return createDefaultContent();
    }

    console.log('[YooptaEditor] Final transformed content:', transformedContent);
    return transformedContent;
  };

  // Create default content structure that matches YooptaEditor expectations
  const createDefaultContent = (): any => {
    const defaultContent = {
      'initial-block': {
        id: 'initial-block',
        type: 'Paragraph',
        value: [
          {
            id: 'initial-element',
            type: 'paragraph',
            children: [{ text: '' }],
            props: {
              nodeType: 'block',
            },
          }
        ],
        meta: {
          order: 0,
          depth: 0
        }
      }
    };

    console.log('[YooptaEditor] Created default content:', defaultContent);
    return defaultContent;
  };

  // Special function to handle backend content format and convert to YooptaEditor format
  const convertBackendContentToYoopta = (backendContent: any): any => {
    console.log('[YooptaEditor] Converting backend content to Yoopta format:', backendContent);

    if (!backendContent || typeof backendContent !== 'object') {
      console.log('[YooptaEditor] Invalid backend content, using default');
      return createDefaultContent();
    }

    const convertedContent: any = {};

    Object.entries(backendContent).forEach(([blockId, block]: [string, any]) => {
      console.log(`[YooptaEditor] Converting backend block ${blockId}:`, block);

      if (!block || typeof block !== 'object') {
        console.log(`[YooptaEditor] Invalid block ${blockId}, skipping`);
        return;
      }

      // Convert the block to proper YooptaEditor format
      const convertedBlock = {
        id: block.id || blockId,
        type: capitalizeBlockType(block.type) || 'Paragraph',
        value: [],
        meta: block.meta || { order: 0, depth: 0 }
      };

      // Handle the value array - this is the critical part
      if (Array.isArray(block.value)) {
        convertedBlock.value = block.value.map((element: any, index: number) => {
          console.log(`[YooptaEditor] Converting backend element ${blockId}.${index}:`, element);

          // BACKEND FORMAT: { text: "content" } -> YOOPTA FORMAT: { id, type, children: [{ text: "content" }], props }
          if (element && typeof element === 'object' && 'text' in element && !element.children) {
            console.log(`[YooptaEditor] Converting simple text element from backend format`);
            return {
              id: `element-${blockId}-${index}`,
              type: 'paragraph',
              children: [{ text: element.text || '' }],
              props: { nodeType: 'block' }
            };
          }

          // Already in proper format
          if (element && element.children && Array.isArray(element.children)) {
            return {
              id: element.id || `element-${blockId}-${index}`,
              type: element.type || 'paragraph',
              children: element.children,
              props: element.props || { nodeType: 'block' }
            };
          }

          // Fallback for any other format
          console.log(`[YooptaEditor] Creating fallback element for ${blockId}.${index}`);
          return {
            id: `element-${blockId}-${index}`,
            type: 'paragraph',
            children: [{ text: '' }],
            props: { nodeType: 'block' }
          };
        });
      } else {
        // No value array, create default
        console.log(`[YooptaEditor] No value array for block ${blockId}, creating default`);
        convertedBlock.value = [{
          id: `element-${blockId}-0`,
          type: 'paragraph',
          children: [{ text: '' }],
          props: { nodeType: 'block' }
        }];
      }

      convertedContent[blockId] = convertedBlock;
    });

    if (Object.keys(convertedContent).length === 0) {
      console.log('[YooptaEditor] No valid blocks converted, using default');
      return createDefaultContent();
    }

    console.log('[YooptaEditor] Backend content converted successfully:', convertedContent);
    return convertedContent;
  };

  // CRITICAL FIX: Start with undefined to prevent premature rendering
  const [value, setValue] = React.useState<any | undefined>(() => {
    console.log('[YooptaEditor] MOUNT: Starting with undefined to prevent premature rendering');
    // Start with undefined to ensure proper initialization sequence
    return undefined;
  });

  // Track if we've initialized with default content
  const [hasInitializedDefault, setHasInitializedDefault] = React.useState(false);
  const [editorError, setEditorError] = React.useState<string | null>(null);
  const [isEditorReady, setIsEditorReady] = React.useState(false);
  const [initializationDelay, setInitializationDelay] = React.useState(true);

  // Handle upload completion and resolve promises
  React.useEffect(() => {
    const uploadPromises = (window as any).__imageUploadPromises;
    if (!uploadPromises) return;

    if (imageUploadError) {
      console.error('[YooptaEditor] Image upload failed:', imageUploadError);

      // Reject all pending promises
      uploadPromises.forEach((promise: any) => {
        promise.reject(new Error(imageUploadError));
      });
      uploadPromises.clear();
    }
  }, [imageUploadError]);

  // Listen for upload success from the saga
  React.useEffect(() => {
    // This is a simplified approach. In a production app, you might want to use
    // redux-saga channels or event emitters for better communication
    const handleUploadSuccess = (event: CustomEvent) => {
      const { s3Url } = event.detail;
      const uploadPromises = (window as any).__imageUploadPromises;

      if (uploadPromises && uploadPromises.size > 0) {
        // Resolve the first pending promise (FIFO)
        const [uploadId, promise] = uploadPromises.entries().next().value;
        promise.resolve(s3Url);
        uploadPromises.delete(uploadId);

        console.log('[YooptaEditor] Upload promise resolved with S3 URL:', s3Url);
      }
    };

    window.addEventListener('imageUploadSuccess', handleUploadSuccess as EventListener);

    return () => {
      window.removeEventListener('imageUploadSuccess', handleUploadSuccess as EventListener);
    };
  }, []);

  // Enhanced content validation function - Fixed to be less strict
  const validateYooptaContent = (content: any): boolean => {
    console.log('[YooptaEditor] Validating content:', content);

    if (!content || typeof content !== 'object') {
      console.log('[YooptaEditor] Content validation failed: not an object');
      return false;
    }

    if (Object.keys(content).length === 0) {
      console.log('[YooptaEditor] Content validation failed: empty object');
      return false;
    }

    const isValid = Object.values(content).every((block: any, blockIndex: number) => {
      console.log(`[YooptaEditor] Validating block ${blockIndex}:`, block);

      if (!block || typeof block !== 'object') {
        console.log(`[YooptaEditor] Block ${blockIndex} validation failed: not an object`);
        return false;
      }

      if (!('id' in block) || !('type' in block) || !('value' in block)) {
        console.log(`[YooptaEditor] Block ${blockIndex} validation failed: missing required properties`);
        return false;
      }

      const blockValue = block.value;
      if (!Array.isArray(blockValue)) {
        console.log(`[YooptaEditor] Block ${blockIndex} validation failed: value is not an array`);
        return false;
      }

      // More lenient validation for block elements
      const elementsValid = blockValue.every((element: any, elemIndex: number) => {
        console.log(`[YooptaEditor] Validating element ${blockIndex}.${elemIndex}:`, element);

        if (!element || typeof element !== 'object') {
          console.log(`[YooptaEditor] Element ${blockIndex}.${elemIndex} validation failed: not an object`);
          return false;
        }

        // For elements with children (text blocks)
        if ('children' in element) {
          const children = element.children;
          if (!Array.isArray(children)) {
            console.log(`[YooptaEditor] Element ${blockIndex}.${elemIndex} validation failed: children not an array`);
            return false;
          }
          const childrenValid = children.every((child: any) =>
            child && typeof child === 'object' && 'text' in child
          );
          if (!childrenValid) {
            console.log(`[YooptaEditor] Element ${blockIndex}.${elemIndex} validation failed: invalid children`);
          }
          return childrenValid;
        }
        // For elements without children (images, dividers, etc.)
        else {
          // Just check that it has some identifying property
          const hasValidProps = 'type' in element || 'id' in element || 'src' in element || 'url' in element;
          if (!hasValidProps) {
            console.log(`[YooptaEditor] Element ${blockIndex}.${elemIndex} validation failed: no valid properties`);
          }
          return hasValidProps;
        }
      });

      if (!elementsValid) {
        console.log(`[YooptaEditor] Block ${blockIndex} validation failed: invalid elements`);
      }
      return elementsValid;
    });

    console.log('[YooptaEditor] Content validation result:', isValid);
    return isValid;
  };

  // Add initialization delay to prevent race conditions
  React.useEffect(() => {
    const timer = setTimeout(() => {
      console.log('[YooptaEditor] Initialization delay completed');
      setInitializationDelay(false);
    }, 100); // Small delay to ensure all components are mounted

    return () => clearTimeout(timer);
  }, []);

  // Track editor readiness
  React.useEffect(() => {
    if (editor && plugins && plugins.length > 0 && value && !initializationDelay) {
      console.log('[YooptaEditor] Editor is ready');
      setIsEditorReady(true);
    } else {
      setIsEditorReady(false);
    }
  }, [editor, plugins, value, initializationDelay]);

  // Timeout to initialize with default content if no content arrives
  React.useEffect(() => {
    if (content === undefined && !hasInitializedDefault && !initializationDelay) {
      console.log('[YooptaEditor] Setting up content timeout...');
      const timeout = setTimeout(() => {
        if (content === undefined && !value) {
          console.log('[YooptaEditor] Timeout reached, initializing with default content');
          const defaultContent = createDefaultContent();
          setValue(defaultContent);
          setHasInitializedDefault(true);
        }
      }, 2000); // Give more time for content to load

      return () => {
        console.log('[YooptaEditor] Clearing content timeout');
        clearTimeout(timeout);
      };
    }
  }, [content, hasInitializedDefault, initializationDelay, value]);

  // Sync internal value with content prop changes
  React.useEffect(() => {
    console.log('[YooptaEditor] Content prop changed:', {
      hasContent: !!content,
      contentType: typeof content,
      content: content,
      currentValue: value,
      hasCurrentValue: !!value,
      hasInitializedDefault
    });

    if (content !== undefined) {
      if (content && typeof content === 'object' && Object.keys(content).length > 0) {
        console.log('[YooptaEditor] Processing content from backend');

        try {
          // First try the new backend conversion function for the common backend format
          const convertedContent = convertBackendContentToYoopta(content);
          console.log('[YooptaEditor] Backend content converted successfully:', convertedContent);

          setValue(convertedContent);
          setHasInitializedDefault(false);
        } catch (error) {
          console.error('[YooptaEditor] Error converting backend content, trying legacy transform:', error);

          try {
            // Fallback to the legacy transformation
            const transformedContent = transformContentForYoopta(content);
            console.log('[YooptaEditor] Legacy content transformed successfully:', transformedContent);

            setValue(transformedContent);
            setHasInitializedDefault(false);
          } catch (legacyError) {
            console.error('[YooptaEditor] Both conversion methods failed, using default:', legacyError);
            const defaultContent = createDefaultContent();
            setValue(defaultContent);
            setHasInitializedDefault(true);
          }
        }
      } else if (content === null || (typeof content === 'object' && Object.keys(content).length === 0)) {
        console.log('[YooptaEditor] Content is null/empty, using default structure');
        const defaultContent = createDefaultContent();
        setValue(defaultContent);
        setHasInitializedDefault(true);
      } else {
        console.log('[YooptaEditor] Content is not a valid object, using default structure');
        const defaultContent = createDefaultContent();
        setValue(defaultContent);
        setHasInitializedDefault(true);
      }
    } else if (!value) {
      console.log('[YooptaEditor] Content is undefined, waiting for actual content...');
    }
  }, [content]);

  // Handle content changes
  const handleChange = (newValue: any) => {
    console.log('[YooptaEditor] Content changed:', {
      blockCount: Object.keys(newValue).length,
      newValue: newValue
    });
    setValue(newValue);
    onChange?.(newValue);
  };

  // Click-to-focus functionality
  const handleContainerClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    // Don't interfere if user clicked on an actual content element
    const target = event.target;

    // Check if target is an Element (has closest method)
    if (!target || !(target instanceof Element)) {
      return;
    }

    // Check if the click was on a contenteditable element or its children
    const isContentEditable = target.closest('[contenteditable="true"]');
    const isYooptaBlock = target.closest('[data-yoopta-block]');
    const isToolbarElement = target.closest('.yoopta-toolbar, .yoopta-action-menu, .yoopta-link-tool');

    // If user clicked on actual content or toolbar, let the default behavior handle it
    if (isContentEditable || isYooptaBlock || isToolbarElement) {
      return;
    }

    // If user clicked on empty space, focus the last block or create a new one
    event.preventDefault();
    focusEditor();
  }, []);

  // Focus the editor - either focus last block or create new paragraph
  const focusEditor = useCallback(() => {
    if (!containerRef.current) return;

    try {
      // First, try to find the last contenteditable element
      const contentEditableElements = containerRef.current.querySelectorAll('[contenteditable="true"]');

      if (contentEditableElements.length > 0) {
        const lastElement = contentEditableElements[contentEditableElements.length - 1] as HTMLElement;

        // Focus the last element
        lastElement.focus();

        // Move cursor to the end of the content
        const range = document.createRange();
        const selection = window.getSelection();

        if (selection && lastElement.childNodes.length > 0) {
          // If there's text content, move to the end
          const lastChild = lastElement.childNodes[lastElement.childNodes.length - 1];
          if (lastChild.nodeType === Node.TEXT_NODE) {
            range.setStart(lastChild, lastChild.textContent?.length || 0);
          } else {
            range.setStartAfter(lastChild);
          }
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
        }

        console.log('[YooptaEditor] Focused last contenteditable element');
      } else {
        // Fallback: try to focus the editor container itself
        const editorElement = containerRef.current.querySelector('[data-yoopta-editor]') as HTMLElement;
        if (editorElement) {
          editorElement.focus();
          console.log('[YooptaEditor] Focused editor container');
        }
      }
    } catch (error) {
      console.error('[YooptaEditor] Error focusing editor:', error);
    }
  }, []);

  // Handle click on specific blocks to focus them
  const handleBlockClick = useCallback((event: React.MouseEvent) => {
    const target = event.target;

    // Check if target is an Element (has closest method)
    if (!target || !(target instanceof Element)) {
      return;
    }

    const blockElement = target.closest('[data-yoopta-block]') as HTMLElement;

    if (blockElement) {
      const contentEditable = blockElement.querySelector('[contenteditable="true"]') as HTMLElement;
      if (contentEditable) {
        // Calculate click position within the block for more precise cursor placement
        const rect = contentEditable.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const clickY = event.clientY - rect.top;

        contentEditable.focus();

        // Try to position cursor at click location
        if (document.caretPositionFromPoint) {
          const caretPosition = document.caretPositionFromPoint(event.clientX, event.clientY);
          if (caretPosition) {
            const range = document.createRange();
            range.setStart(caretPosition.offsetNode, caretPosition.offset);
            range.collapse(true);

            const selection = window.getSelection();
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(range);
            }
          }
        } else if ((document as any).caretRangeFromPoint) {
          // Fallback for browsers that support caretRangeFromPoint
          const range = (document as any).caretRangeFromPoint(event.clientX, event.clientY);
          if (range) {
            const selection = window.getSelection();
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(range);
            }
          }
        }

        console.log('[YooptaEditor] Focused specific block at click position');
      }
    }
  }, []);

  // Reset error when content changes
  React.useEffect(() => {
    if (editorError) {
      setEditorError(null);
    }
  }, [content]);

  const renderEditor = () => {
    try {
      if (!value) {
        console.log('[YooptaEditor] No value, showing loading state');
        return (
          <div className="notion-editor-loading p-8 text-center">
            <div className="text-gray-500">
              {content === undefined ? 'Waiting for content...' : 'Initializing editor...'}
            </div>
          </div>
        );
      }

      // Additional validation before rendering
      if (!editor || !plugins || plugins.length === 0) {
        console.log('[YooptaEditor] Editor or plugins not ready', {
          hasEditor: !!editor,
          hasPlugins: !!plugins,
          pluginCount: plugins?.length || 0
        });
        return (
          <div className="notion-editor-loading p-8 text-center">
            <div className="text-gray-500">Preparing editor...</div>
          </div>
        );
      }

      console.log('[YooptaEditor] Rendering YooptaEditor with value:', value);
      console.log('[YooptaEditor] Editor instance:', editor);
      console.log('[YooptaEditor] Plugins count:', plugins.length);

      return (
        <div className="yoopta-editor-wrapper">
          <ErrorBoundary
            fallback={
              <div className="notion-editor-error p-4 border border-red-200 rounded-md bg-red-50">
                <p className="text-red-600 font-medium">Editor Error</p>
                <p className="text-red-500 text-sm mt-1">
                  The editor encountered an error. This might be due to incompatible content format.
                </p>
                <button
                  onClick={() => {
                    setEditorError(null);
                    const defaultContent = createDefaultContent();
                    setValue(defaultContent);
                    setHasInitializedDefault(true);
                  }}
                  className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
                >
                  Reset Editor
                </button>
              </div>
            }
          >
            {editor && value && validateYooptaContent(value) && (
              <YooptaEditor
                editor={editor}
                plugins={plugins as any}
                tools={TOOLS}
                marks={MARKS}
                value={value}
                onChange={handleChange}
                placeholder={placeholder}
                readOnly={readOnly}
                autoFocus={!readOnly}
                className="notion-style-editor !w-full"
              />
            )}
          </ErrorBoundary>
        </div>
      );
    } catch (error) {
      console.error('[YooptaEditor] Error rendering editor:', error);
      setEditorError(error instanceof Error ? error.message : 'Unknown editor error');
      return (
        <div className="notion-editor-error p-4 border border-red-200 rounded-md bg-red-50">
          <p className="text-red-600 font-medium">Editor Error</p>
          <p className="text-red-500 text-sm mt-1">
            {error instanceof Error ? error.message : 'An unknown error occurred'}
          </p>
          <button
            onClick={() => {
              setEditorError(null);
              const defaultContent = createDefaultContent();
              setValue(defaultContent);
              setHasInitializedDefault(true);
            }}
            className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
          >
            Reset Editor
          </button>
        </div>
      );
    }
  };

  // Critical check: Don't render anything if we don't have a valid value and ready editor
  const shouldRender = value !== undefined && value !== null && isEditorReady && !initializationDelay;

  console.log('[YooptaEditor] Final render decision:', {
    shouldRender,
    hasValue: !!value,
    valueType: typeof value,
    hasEditorError: !!editorError,
    isEditorReady,
    hasEditor: !!editor,
    hasPlugins: !!plugins,
    pluginCount: plugins?.length || 0,
    initializationDelay,
    content,
    hasInitializedDefault
  });

  if (!shouldRender && !editorError) {
    console.log('[YooptaEditor] Not rendering - waiting for editor to be ready');
    return (
      <div className={`notion-editor-container ${className}`}>
        <div className="notion-editor-loading p-8 text-center">
          <div className="text-gray-500">
            {initializationDelay ? 'Starting up...' :
             !editor ? 'Initializing editor...' :
             !plugins || plugins.length === 0 ? 'Loading plugins...' :
             !value ? 'Preparing content...' : 'Getting ready...'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`notion-editor-container ${className}`}
      onClick={handleContainerClick}
      onMouseDown={handleBlockClick}
    >
      {/* CSS to disable link hover effects and tooltips */}
      <style jsx>{`
        .notion-editor-container :global(a[href]) {
          /* Disable browser tooltips */
          pointer-events: auto !important;
        }

        .notion-editor-container :global(a[href]:hover) {
          /* Prevent any hover-based popups or tooltips */
          position: relative !important;
        }

        /* Hide any Yoopta link tool popups */
        .notion-editor-container :global(.yoopta-link-tool),
        .notion-editor-container :global([data-yoopta-link-tool]),
        .notion-editor-container :global(.link-tool-popup),
        .notion-editor-container :global(.link-preview),
        .notion-editor-container :global(.link-tooltip) {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
        }
      `}</style>

      {editorError ? (
        <div className="notion-editor-error p-4 border border-red-200 rounded-md bg-red-50">
          <p className="text-red-600 font-medium">Editor Error</p>
          <p className="text-red-500 text-sm mt-1">{editorError}</p>
          <button
            onClick={() => {
              setEditorError(null);
              const defaultContent = createDefaultContent();
              setValue(defaultContent);
              setHasInitializedDefault(true);
            }}
            className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
          >
            Reset Editor
          </button>
        </div>
      ) : (
        renderEditor()
      )}

      {loading && (
        <div className="absolute top-2 right-2 text-sm text-gray-500 flex items-center gap-1">
          <div className="w-3 h-3 border border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
          Saving...
        </div>
      )}
    </div>
  );
};

export default YooptaRichTextEditor;
